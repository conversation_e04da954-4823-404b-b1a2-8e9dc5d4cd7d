"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: function() { return /* binding */ AuthService; }\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _services_adminSettings__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./services/adminSettings */ \"(app-pages-browser)/./src/lib/services/adminSettings.ts\");\n/* harmony import */ var _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./services/referralSystem */ \"(app-pages-browser)/./src/lib/services/referralSystem.ts\");\n\n\n\nclass AuthService {\n    /**\n   * Sign up a new user with OTP verification and referral processing\n   */ static async signUp(email, password, userData) {\n        // Check if email verification is required (with fallback to false if table doesn't exist)\n        let requireEmailVerification = false;\n        try {\n            requireEmailVerification = await _services_adminSettings__WEBPACK_IMPORTED_MODULE_1__.AdminSettingsService.getSetting(\"require_email_verification\") || false;\n        } catch (error) {\n            // Gracefully handle admin settings access failures\n            console.warn(\"Could not fetch email verification setting, defaulting to false:\", error);\n            requireEmailVerification = false;\n            // If it's a 406 error or RLS policy error, it means the table exists but access is restricted\n            // This is expected during signup when user is not authenticated yet\n            if (error instanceof Error && (error.message.includes(\"406\") || error.message.includes(\"policy\"))) {\n                console.info(\"Admin settings access restricted during signup - using default values\");\n            }\n        }\n        // Validate referral code if provided\n        let referrer = null;\n        if (userData === null || userData === void 0 ? void 0 : userData.referralCode) {\n            try {\n                referrer = await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.validateReferralCode(userData.referralCode);\n                if (!referrer) {\n                    throw new Error(\"Invalid referral code\");\n                }\n            } catch (error) {\n                console.warn(\"Could not validate referral code:\", error);\n            // Don't fail signup for referral code issues, just log the warning\n            }\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    ...userData,\n                    referral_code: userData === null || userData === void 0 ? void 0 : userData.referralCode,\n                    full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n                    phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n                    location: userData === null || userData === void 0 ? void 0 : userData.location,\n                    referrer_id: referrer === null || referrer === void 0 ? void 0 : referrer.id\n                }\n            }\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        // Always create user profile immediately (regardless of email verification)\n        if (data.user) {\n            try {\n                // Create profile with referral information if available\n                if (referrer) {\n                    await this.createUserProfileWithReferrer(data.user, userData, referrer);\n                } else {\n                    await this.createBasicUserProfile(data.user, userData);\n                }\n            } catch (profileError) {\n                console.error(\"Error creating user profile:\", profileError);\n            // Don't fail the signup, but log the error\n            }\n        }\n        return {\n            data,\n            requireEmailVerification,\n            referrer\n        };\n    }\n    /**\n   * Create basic user profile without complex operations (fast)\n   * Uses server-side API route to bypass RLS issues during signup\n   */ static async createBasicUserProfile(authUser, userData) {\n        console.log(\"Creating basic user profile for:\", authUser.email);\n        const maxRetries = 3;\n        let lastError = null;\n        for(let attempt = 1; attempt <= maxRetries; attempt++){\n            try {\n                console.log(\"Profile creation attempt \".concat(attempt, \"/\").concat(maxRetries));\n                // Use server-side API route for profile creation\n                const response = await fetch(\"/api/auth/create-profile\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        userId: authUser.id,\n                        email: authUser.email,\n                        userData: {\n                            full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n                            phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n                            location: userData === null || userData === void 0 ? void 0 : userData.location,\n                            referral_level: 0,\n                            referred_by_id: null // Will be updated later if referrer exists\n                        }\n                    })\n                });\n                if (!response.ok) {\n                    const errorData = await response.json();\n                    const errorMessage = errorData.error || \"Failed to create user profile\";\n                    // Don't retry for client errors (4xx)\n                    if (response.status >= 400 && response.status < 500) {\n                        throw new Error(errorMessage);\n                    }\n                    // Retry for server errors (5xx)\n                    lastError = new Error(errorMessage);\n                    console.warn(\"Profile creation attempt \".concat(attempt, \" failed:\"), errorMessage);\n                    if (attempt < maxRetries) {\n                        // Wait before retry with exponential backoff\n                        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);\n                        console.log(\"Retrying in \".concat(delay, \"ms...\"));\n                        await new Promise((resolve)=>setTimeout(resolve, delay));\n                        continue;\n                    }\n                } else {\n                    const result = await response.json();\n                    console.log(\"✅ Basic user profile created successfully:\", result.message);\n                    return; // Success, exit the retry loop\n                }\n            } catch (error) {\n                lastError = error instanceof Error ? error : new Error(\"Unknown error\");\n                console.error(\"Profile creation attempt \".concat(attempt, \" failed:\"), lastError.message);\n                // Don't retry for network errors that are likely permanent\n                if (lastError.message.includes(\"fetch\") && attempt === 1) {\n                    break;\n                }\n                if (attempt < maxRetries) {\n                    // Wait before retry\n                    const delay = Math.min(1000 * attempt, 3000);\n                    await new Promise((resolve)=>setTimeout(resolve, delay));\n                }\n            }\n        }\n        // If we get here, all attempts failed\n        console.error(\"All profile creation attempts failed\");\n        throw lastError || new Error(\"Failed to create user profile after multiple attempts\");\n    }\n    /**\n   * Schedule referral placement for later processing (non-blocking)\n   */ static async scheduleReferralPlacement(userId, referrerId) {\n        try {\n            // Use a timeout to defer this operation\n            setTimeout(async ()=>{\n                try {\n                    await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.placeUserInHierarchy(userId, referrerId);\n                    console.log(\"Referral placement completed for user:\", userId);\n                } catch (error) {\n                    console.error(\"Deferred referral placement failed:\", error);\n                }\n            }, 2000) // 2 second delay\n            ;\n        } catch (error) {\n            console.error(\"Failed to schedule referral placement:\", error);\n        }\n    }\n    /**\n   * Create user profile in public.users table (legacy method with full operations)\n   */ static async createUserProfile(authUser, userData, referrer) {\n        try {\n            // Use server-side API route for profile creation with referrer data\n            const response = await fetch(\"/api/auth/create-profile\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: authUser.id,\n                    email: authUser.email,\n                    userData: {\n                        full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n                        phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n                        location: userData === null || userData === void 0 ? void 0 : userData.location,\n                        referral_level: referrer ? (referrer.referral_level || 0) + 1 : 0,\n                        referred_by_id: (referrer === null || referrer === void 0 ? void 0 : referrer.id) || null\n                    }\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"Failed to create user profile\");\n            }\n            // If user has a referrer, place them in hierarchy\n            if (referrer) {\n                try {\n                    await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.placeUserInHierarchy(authUser.id, referrer.id);\n                } catch (referralError) {\n                    console.error(\"Failed to place user in referral hierarchy:\", referralError);\n                // Don't fail the profile creation, but log the error\n                }\n            }\n        } catch (error) {\n            console.error(\"Error creating user profile via API:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Verify email OTP and process referral placement\n   */ static async verifyEmailOtp(email, token) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.verifyOtp({\n            email,\n            token,\n            type: \"signup\"\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        // After successful verification, create user profile if it doesn't exist\n        if (data.user) {\n            try {\n                // Check if user profile already exists\n                const { data: existingUser } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"id\").eq(\"id\", data.user.id).single();\n                if (!existingUser) {\n                    var _data_user_user_metadata, _data_user_user_metadata1, _data_user_user_metadata2, _data_user_user_metadata3, _data_user_user_metadata4;\n                    // Get referrer if referral code exists\n                    let referrer = null;\n                    if ((_data_user_user_metadata = data.user.user_metadata) === null || _data_user_user_metadata === void 0 ? void 0 : _data_user_user_metadata.referral_code) {\n                        referrer = await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.validateReferralCode(data.user.user_metadata.referral_code);\n                    }\n                    // Create user profile\n                    await this.createUserProfile(data.user, {\n                        full_name: (_data_user_user_metadata1 = data.user.user_metadata) === null || _data_user_user_metadata1 === void 0 ? void 0 : _data_user_user_metadata1.full_name,\n                        phone: (_data_user_user_metadata2 = data.user.user_metadata) === null || _data_user_user_metadata2 === void 0 ? void 0 : _data_user_user_metadata2.phone,\n                        location: (_data_user_user_metadata3 = data.user.user_metadata) === null || _data_user_user_metadata3 === void 0 ? void 0 : _data_user_user_metadata3.location,\n                        referralCode: (_data_user_user_metadata4 = data.user.user_metadata) === null || _data_user_user_metadata4 === void 0 ? void 0 : _data_user_user_metadata4.referral_code\n                    }, referrer);\n                }\n            } catch (profileError) {\n                console.error(\"Error creating user profile after verification:\", profileError);\n            // Don't fail the verification, but log the error\n            }\n        }\n        return data;\n    }\n    /**\n   * Resend email OTP\n   */ static async resendEmailOtp(email) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.resend({\n            type: \"signup\",\n            email\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Sign in with email and password\n   */ static async signIn(email, password) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Sign out the current user\n   */ static async signOut() {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut();\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Get the current user session\n   */ static async getSession() {\n        const { data: { session }, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return session;\n    }\n    /**\n   * Get the current user with improved error handling\n   */ static async getCurrentUser() {\n        try {\n            // First check if we have a valid session\n            const { data: { session }, error: sessionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (sessionError) {\n                console.error(\"Session error:\", sessionError);\n                return null;\n            }\n            if (!(session === null || session === void 0 ? void 0 : session.user)) {\n                console.log(\"No active session found\");\n                return null;\n            }\n            const user = session.user;\n            console.log(\"Found active session for user:\", user.email);\n            // Get additional user data from users table with retry logic\n            let retryCount = 0;\n            const maxRetries = 3;\n            while(retryCount < maxRetries){\n                try {\n                    const { data: profile, error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"*\").eq(\"id\", user.id).single();\n                    if (profileError) {\n                        if (profileError.code === \"PGRST116\") {\n                            var _user_user_metadata, _user_user_metadata1, _user_user_metadata2;\n                            // User profile doesn't exist, create it\n                            console.log(\"User profile not found, creating...\");\n                            const newProfile = {\n                                id: user.id,\n                                email: user.email,\n                                full_name: ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || null,\n                                phone: ((_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.phone) || null,\n                                location: ((_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.location) || null,\n                                role: \"user\",\n                                is_super_admin: false\n                            };\n                            const { data: createdProfile, error: createError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").insert(newProfile).select().single();\n                            if (createError) {\n                                console.error(\"Error creating user profile:\", createError);\n                                return {\n                                    id: user.id,\n                                    email: user.email\n                                };\n                            }\n                            return createdProfile;\n                        } else {\n                            throw profileError;\n                        }\n                    }\n                    return profile;\n                } catch (error) {\n                    retryCount++;\n                    console.error(\"Error fetching user profile (attempt \".concat(retryCount, \"):\"), error);\n                    if (retryCount >= maxRetries) {\n                        console.error(\"Max retries reached, returning basic user info\");\n                        return {\n                            id: user.id,\n                            email: user.email\n                        };\n                    }\n                    // Wait before retry\n                    await new Promise((resolve)=>setTimeout(resolve, 1000 * retryCount));\n                }\n            }\n            return null;\n        } catch (error) {\n            console.error(\"Error in getCurrentUser:\", error);\n            return null;\n        }\n    }\n    /**\n   * Update user profile\n   */ static async updateProfile(userId, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").update(updates).eq(\"id\", userId).select().single();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Reset password\n   */ static async resetPassword(email) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Update password\n   */ static async updatePassword(newPassword) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.updateUser({\n            password: newPassword\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Listen to auth state changes with improved handling\n   */ static onAuthStateChange(callback) {\n        return _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            var _session_user;\n            console.log(\"Auth state change detected:\", event, session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.email);\n            // Add a small delay to ensure state consistency\n            setTimeout(()=>{\n                callback(event, session);\n            }, 100);\n        });\n    }\n    /**\n   * Check if current session is valid\n   */ static async isSessionValid() {\n        try {\n            const { data: { session }, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (error || !session) {\n                return false;\n            }\n            // Check if token is expired\n            const now = Math.floor(Date.now() / 1000);\n            if (session.expires_at && session.expires_at < now) {\n                console.log(\"Session token expired\");\n                return false;\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Error checking session validity:\", error);\n            return false;\n        }\n    }\n    /**\n   * Refresh session if needed\n   */ static async refreshSession() {\n        try {\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.refreshSession();\n            if (error) {\n                console.error(\"Error refreshing session:\", error);\n                return false;\n            }\n            return !!data.session;\n        } catch (error) {\n            console.error(\"Error in refreshSession:\", error);\n            return false;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/auth.ts\n"));

/***/ })

});