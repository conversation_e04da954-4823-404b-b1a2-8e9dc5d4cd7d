# OKDOI Referral & Commission System

## Overview

The OKDOI Referral & Commission System is a comprehensive multi-level marketing (MLM) system that manages user referrals, hierarchical placement, and commission distribution across 10 levels. The system supports different user types including OKDOI Head, Zonal Managers (ZM), Regional Sales Managers (RSM), and regular users.

## System Architecture

### User Hierarchy
```
OKDOI Head
├── Zonal Manager 1
│   ├── RSM 1.1
│   │   ├── User 1.1.1
│   │   ├── User 1.1.2
│   │   └── User 1.1.3
│   └── RSM 1.2
└── Zonal Manager 2
    └── ...
```

### Key Features

1. **Automatic Placement Algorithm**: Left-to-right placement with spillover when positions are full
2. **3-Position Limit**: Each user can have maximum 3 direct referrals
3. **10-Level Commission Distribution**: Commissions distributed across 10 levels with fallback to OKDOI Head
4. **Multiple Commission Types**: Direct commission, level commission, RSM bonus, ZM bonus, and OKDOI Head commission
5. **Real-time Processing**: Automatic commission calculation and wallet integration

## Database Schema

### Core Tables

#### `users` (Extended)
- `user_type`: 'okdoi_head' | 'zonal_manager' | 'rsm' | 'user'
- `referral_code`: Unique referral code for ZM and RSM users
- `referred_by_id`: Reference to the user who referred this user
- `referral_level`: Current level in the hierarchy
- `referral_path`: Full path from OKDOI Head
- `direct_referrals_count`: Number of direct referrals
- `total_downline_count`: Total users in downline
- `total_commission_earned`: Total commission earned
- `is_referral_active`: Whether user can receive referrals

#### `referral_hierarchy`
Tracks the complete referral hierarchy relationships.

#### `commission_structure`
Defines commission rates for different package values and types.

#### `commission_transactions`
Records all commission transactions with status tracking.

#### `referral_placements`
Manages the 3-position placement system.

#### `zonal_managers`
Manages Zonal Manager information and territories.

#### `regional_sales_managers`
Manages RSM information and relationships.

## API Endpoints

### Admin Endpoints
- `POST /api/admin/process-commissions` - Process pending commissions
- `GET /api/admin/process-commissions` - Get commission processing status

### User Services
- `ReferralSystemService` - Core referral management
- `CommissionSystemService` - Commission calculation and distribution

## Commission Structure

### Package Values & Rates
| Package Value | Direct Commission | Level Commission | RSM Bonus | ZM Bonus | OKDOI Head |
|---------------|-------------------|------------------|-----------|----------|------------|
| Rs 2,000      | 10%               | 2%               | 2.5%      | 2%       | 2.5%       |
| Rs 5,000      | 25%               | 2%               | 2.5%      | 2%       | 2%         |
| Rs 10,000     | 25%               | 2%               | 2.8%      | 2%       | 2%         |
| Rs 50,000     | 25%               | 2%               | 2.8%      | 2%       | 2%         |

### Commission Distribution Logic
1. When a user purchases a subscription, commissions are calculated for all 10 levels
2. If a level has no beneficiary, the commission goes to OKDOI Head
3. Commissions are initially marked as 'pending'
4. A background process credits commissions to user wallets
5. Successfully processed commissions are marked as 'processed'

## User Interface

### Admin Panel
- **Referral System**: Manage ZMs and RSMs, view hierarchy
- **Commission Structure**: Configure commission rates
- **Commission Reports**: Analytics and reporting
- **Commission Payouts**: Process pending commissions

### User Dashboard
- **Referrals Page**: View referral tree, stats, and commission history
- **Referral Link**: Share referral link with unique code
- **Commission Tracking**: Real-time commission earnings

## Testing

### Test Suites
1. **Unit Tests**: Core functionality testing
2. **Performance Tests**: Load and stress testing
3. **Data Integrity**: Validation of data consistency

### Running Tests
```bash
# Run all referral system tests
npm run test:referral-all

# Run specific test suites
npm run test:referral
npm run test:performance

# Validate data integrity
npm run validate:referral
```

## Installation & Setup

### Database Migration
```sql
-- Run the referral system migration
-- File: supabase/migrations/032_referral_commission_system.sql
```

### Environment Variables
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### Initial Data Setup
The migration automatically creates:
- Commission structure data
- Database functions for placement and commission calculation
- Row Level Security policies
- Triggers for automatic referral code generation

## Usage Examples

### Creating a Zonal Manager
```typescript
await ReferralSystemService.createZonalManager(
  userId,
  'Western Zone',
  'Covers Colombo, Gampaha, Kalutara',
  ['colombo', 'gampaha', 'kalutara']
)
```

### Upgrading User to RSM
```typescript
await ReferralSystemService.upgradeToRSM(
  userId,
  zonalManagerId,
  'Colombo Region'
)
```

### Processing Commissions
```typescript
// Distribute commissions for a subscription purchase
await CommissionSystemService.distributeCommissions(
  purchaserId,
  subscriptionId,
  packageAmount
)

// Process pending commissions
await CommissionSystemService.processPendingCommissions(100)
```

## Monitoring & Maintenance

### Data Integrity Checks
- Referral hierarchy consistency
- Commission transaction accuracy
- User count validation
- Referral code uniqueness

### Performance Monitoring
- Commission processing time
- Hierarchy placement efficiency
- Database query optimization

### Error Handling
- Failed commission transactions are logged
- Automatic retry mechanisms
- Admin notifications for critical issues

## Security Considerations

### Row Level Security (RLS)
- Users can only view their own referral data
- Admins have full access to all data
- Commission transactions are protected

### Data Validation
- Referral code format validation
- Commission rate bounds checking
- User type consistency enforcement

## Future Enhancements

1. **Automated Commission Processing**: Scheduled background jobs
2. **Advanced Analytics**: Machine learning insights
3. **Mobile App Integration**: React Native support
4. **Blockchain Integration**: Transparent commission tracking
5. **Gamification**: Achievement badges and leaderboards

## Support & Documentation

For technical support or questions about the referral system:
1. Check the test suites for usage examples
2. Review the database schema documentation
3. Run data integrity validation scripts
4. Contact the development team

## License

This referral system is part of the OKDOI Marketplace application and is proprietary software.
