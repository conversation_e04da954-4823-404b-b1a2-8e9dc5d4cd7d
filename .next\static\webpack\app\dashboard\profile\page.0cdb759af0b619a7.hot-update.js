"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/profile/page",{

/***/ "(app-pages-browser)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: function() { return /* binding */ AuthService; }\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _services_adminSettings__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./services/adminSettings */ \"(app-pages-browser)/./src/lib/services/adminSettings.ts\");\n/* harmony import */ var _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./services/referralSystem */ \"(app-pages-browser)/./src/lib/services/referralSystem.ts\");\n\n\n\nclass AuthService {\n    /**\n   * Sign up a new user with OTP verification and referral processing\n   */ static async signUp(email, password, userData) {\n        // Check if email verification is required (with fallback to false if table doesn't exist)\n        let requireEmailVerification = false;\n        try {\n            requireEmailVerification = await _services_adminSettings__WEBPACK_IMPORTED_MODULE_1__.AdminSettingsService.getSetting(\"require_email_verification\") || false;\n        } catch (error) {\n            // Gracefully handle admin settings access failures\n            console.warn(\"Could not fetch email verification setting, defaulting to false:\", error);\n            requireEmailVerification = false;\n            // If it's a 406 error or RLS policy error, it means the table exists but access is restricted\n            // This is expected during signup when user is not authenticated yet\n            if (error instanceof Error && (error.message.includes(\"406\") || error.message.includes(\"policy\"))) {\n                console.info(\"Admin settings access restricted during signup - using default values\");\n            }\n        }\n        // Validate referral code if provided\n        let referrer = null;\n        if (userData === null || userData === void 0 ? void 0 : userData.referralCode) {\n            try {\n                referrer = await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.validateReferralCode(userData.referralCode);\n                if (!referrer) {\n                    throw new Error(\"Invalid referral code\");\n                }\n            } catch (error) {\n                console.warn(\"Could not validate referral code:\", error);\n            // Don't fail signup for referral code issues, just log the warning\n            }\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    ...userData,\n                    referral_code: userData === null || userData === void 0 ? void 0 : userData.referralCode,\n                    full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n                    phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n                    location: userData === null || userData === void 0 ? void 0 : userData.location,\n                    referrer_id: referrer === null || referrer === void 0 ? void 0 : referrer.id\n                }\n            }\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        // Always create user profile immediately (regardless of email verification)\n        if (data.user) {\n            try {\n                // Create profile with referral information if available\n                if (referrer) {\n                    await this.createUserProfileWithReferrer(data.user, userData, referrer);\n                } else {\n                    await this.createBasicUserProfile(data.user, userData);\n                }\n            } catch (profileError) {\n                console.error(\"Error creating user profile:\", profileError);\n            // Don't fail the signup, but log the error\n            }\n        }\n        return {\n            data,\n            requireEmailVerification,\n            referrer\n        };\n    }\n    /**\n   * Create user profile with referrer information and hierarchy placement\n   */ static async createUserProfileWithReferrer(authUser, userData, referrer) {\n        console.log(\"Creating user profile with referrer for:\", authUser.email);\n        try {\n            // Use server-side API route for profile creation with referrer data\n            const response = await fetch(\"/api/auth/create-profile\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: authUser.id,\n                    email: authUser.email,\n                    userData: {\n                        full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n                        phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n                        location: userData === null || userData === void 0 ? void 0 : userData.location,\n                        referral_level: referrer ? (referrer.referral_level || 0) + 1 : 0,\n                        referred_by_id: (referrer === null || referrer === void 0 ? void 0 : referrer.id) || null\n                    }\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"Failed to create user profile\");\n            }\n            // Place user in referral hierarchy immediately\n            if (referrer) {\n                try {\n                    await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.placeUserInHierarchy(authUser.id, referrer.id);\n                    console.log(\"✅ User placed in referral hierarchy successfully\");\n                } catch (referralError) {\n                    console.error(\"Failed to place user in referral hierarchy:\", referralError);\n                // Don't fail the profile creation, but log the error\n                }\n            }\n            console.log(\"✅ User profile with referrer created successfully\");\n        } catch (error) {\n            console.error(\"Error creating user profile with referrer:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create basic user profile without complex operations (fast)\n   * Uses server-side API route to bypass RLS issues during signup\n   */ static async createBasicUserProfile(authUser, userData) {\n        console.log(\"Creating basic user profile for:\", authUser.email);\n        const maxRetries = 3;\n        let lastError = null;\n        for(let attempt = 1; attempt <= maxRetries; attempt++){\n            try {\n                console.log(\"Profile creation attempt \".concat(attempt, \"/\").concat(maxRetries));\n                // Use server-side API route for profile creation\n                const response = await fetch(\"/api/auth/create-profile\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        userId: authUser.id,\n                        email: authUser.email,\n                        userData: {\n                            full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n                            phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n                            location: userData === null || userData === void 0 ? void 0 : userData.location,\n                            referral_level: 0,\n                            referred_by_id: null // Will be updated later if referrer exists\n                        }\n                    })\n                });\n                if (!response.ok) {\n                    const errorData = await response.json();\n                    const errorMessage = errorData.error || \"Failed to create user profile\";\n                    // Don't retry for client errors (4xx)\n                    if (response.status >= 400 && response.status < 500) {\n                        throw new Error(errorMessage);\n                    }\n                    // Retry for server errors (5xx)\n                    lastError = new Error(errorMessage);\n                    console.warn(\"Profile creation attempt \".concat(attempt, \" failed:\"), errorMessage);\n                    if (attempt < maxRetries) {\n                        // Wait before retry with exponential backoff\n                        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);\n                        console.log(\"Retrying in \".concat(delay, \"ms...\"));\n                        await new Promise((resolve)=>setTimeout(resolve, delay));\n                        continue;\n                    }\n                } else {\n                    const result = await response.json();\n                    console.log(\"✅ Basic user profile created successfully:\", result.message);\n                    return; // Success, exit the retry loop\n                }\n            } catch (error) {\n                lastError = error instanceof Error ? error : new Error(\"Unknown error\");\n                console.error(\"Profile creation attempt \".concat(attempt, \" failed:\"), lastError.message);\n                // Don't retry for network errors that are likely permanent\n                if (lastError.message.includes(\"fetch\") && attempt === 1) {\n                    break;\n                }\n                if (attempt < maxRetries) {\n                    // Wait before retry\n                    const delay = Math.min(1000 * attempt, 3000);\n                    await new Promise((resolve)=>setTimeout(resolve, delay));\n                }\n            }\n        }\n        // If we get here, all attempts failed\n        console.error(\"All profile creation attempts failed\");\n        throw lastError || new Error(\"Failed to create user profile after multiple attempts\");\n    }\n    /**\n   * Schedule referral placement for later processing (non-blocking)\n   */ static async scheduleReferralPlacement(userId, referrerId) {\n        try {\n            // Use a timeout to defer this operation\n            setTimeout(async ()=>{\n                try {\n                    await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.placeUserInHierarchy(userId, referrerId);\n                    console.log(\"Referral placement completed for user:\", userId);\n                } catch (error) {\n                    console.error(\"Deferred referral placement failed:\", error);\n                }\n            }, 2000) // 2 second delay\n            ;\n        } catch (error) {\n            console.error(\"Failed to schedule referral placement:\", error);\n        }\n    }\n    /**\n   * Create user profile in public.users table (legacy method with full operations)\n   */ static async createUserProfile(authUser, userData, referrer) {\n        try {\n            // Use server-side API route for profile creation with referrer data\n            const response = await fetch(\"/api/auth/create-profile\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: authUser.id,\n                    email: authUser.email,\n                    userData: {\n                        full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n                        phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n                        location: userData === null || userData === void 0 ? void 0 : userData.location,\n                        referral_level: referrer ? (referrer.referral_level || 0) + 1 : 0,\n                        referred_by_id: (referrer === null || referrer === void 0 ? void 0 : referrer.id) || null\n                    }\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"Failed to create user profile\");\n            }\n            // If user has a referrer, place them in hierarchy\n            if (referrer) {\n                try {\n                    await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.placeUserInHierarchy(authUser.id, referrer.id);\n                } catch (referralError) {\n                    console.error(\"Failed to place user in referral hierarchy:\", referralError);\n                // Don't fail the profile creation, but log the error\n                }\n            }\n        } catch (error) {\n            console.error(\"Error creating user profile via API:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Verify email OTP and process referral placement\n   */ static async verifyEmailOtp(email, token) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.verifyOtp({\n            email,\n            token,\n            type: \"signup\"\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        // After successful verification, create user profile if it doesn't exist\n        if (data.user) {\n            try {\n                // Check if user profile already exists\n                const { data: existingUser } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"id\").eq(\"id\", data.user.id).single();\n                if (!existingUser) {\n                    var _data_user_user_metadata, _data_user_user_metadata1, _data_user_user_metadata2, _data_user_user_metadata3, _data_user_user_metadata4;\n                    // Get referrer if referral code exists\n                    let referrer = null;\n                    if ((_data_user_user_metadata = data.user.user_metadata) === null || _data_user_user_metadata === void 0 ? void 0 : _data_user_user_metadata.referral_code) {\n                        referrer = await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.validateReferralCode(data.user.user_metadata.referral_code);\n                    }\n                    // Create user profile\n                    await this.createUserProfile(data.user, {\n                        full_name: (_data_user_user_metadata1 = data.user.user_metadata) === null || _data_user_user_metadata1 === void 0 ? void 0 : _data_user_user_metadata1.full_name,\n                        phone: (_data_user_user_metadata2 = data.user.user_metadata) === null || _data_user_user_metadata2 === void 0 ? void 0 : _data_user_user_metadata2.phone,\n                        location: (_data_user_user_metadata3 = data.user.user_metadata) === null || _data_user_user_metadata3 === void 0 ? void 0 : _data_user_user_metadata3.location,\n                        referralCode: (_data_user_user_metadata4 = data.user.user_metadata) === null || _data_user_user_metadata4 === void 0 ? void 0 : _data_user_user_metadata4.referral_code\n                    }, referrer);\n                }\n            } catch (profileError) {\n                console.error(\"Error creating user profile after verification:\", profileError);\n            // Don't fail the verification, but log the error\n            }\n        }\n        return data;\n    }\n    /**\n   * Resend email OTP\n   */ static async resendEmailOtp(email) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.resend({\n            type: \"signup\",\n            email\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Sign in with email and password\n   */ static async signIn(email, password) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Sign out the current user\n   */ static async signOut() {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut();\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Get the current user session\n   */ static async getSession() {\n        const { data: { session }, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return session;\n    }\n    /**\n   * Get the current user with improved error handling\n   */ static async getCurrentUser() {\n        try {\n            // First check if we have a valid session\n            const { data: { session }, error: sessionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (sessionError) {\n                console.error(\"Session error:\", sessionError);\n                return null;\n            }\n            if (!(session === null || session === void 0 ? void 0 : session.user)) {\n                console.log(\"No active session found\");\n                return null;\n            }\n            const user = session.user;\n            console.log(\"Found active session for user:\", user.email);\n            // Get additional user data from users table with retry logic\n            let retryCount = 0;\n            const maxRetries = 3;\n            while(retryCount < maxRetries){\n                try {\n                    const { data: profile, error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"*\").eq(\"id\", user.id).single();\n                    if (profileError) {\n                        if (profileError.code === \"PGRST116\") {\n                            var _user_user_metadata, _user_user_metadata1, _user_user_metadata2;\n                            // User profile doesn't exist, create it\n                            console.log(\"User profile not found, creating...\");\n                            const newProfile = {\n                                id: user.id,\n                                email: user.email,\n                                full_name: ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || null,\n                                phone: ((_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.phone) || null,\n                                location: ((_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.location) || null,\n                                role: \"user\",\n                                is_super_admin: false\n                            };\n                            const { data: createdProfile, error: createError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").insert(newProfile).select().single();\n                            if (createError) {\n                                console.error(\"Error creating user profile:\", createError);\n                                return {\n                                    id: user.id,\n                                    email: user.email\n                                };\n                            }\n                            return createdProfile;\n                        } else {\n                            throw profileError;\n                        }\n                    }\n                    return profile;\n                } catch (error) {\n                    retryCount++;\n                    console.error(\"Error fetching user profile (attempt \".concat(retryCount, \"):\"), error);\n                    if (retryCount >= maxRetries) {\n                        console.error(\"Max retries reached, returning basic user info\");\n                        return {\n                            id: user.id,\n                            email: user.email\n                        };\n                    }\n                    // Wait before retry\n                    await new Promise((resolve)=>setTimeout(resolve, 1000 * retryCount));\n                }\n            }\n            return null;\n        } catch (error) {\n            console.error(\"Error in getCurrentUser:\", error);\n            return null;\n        }\n    }\n    /**\n   * Update user profile\n   */ static async updateProfile(userId, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").update(updates).eq(\"id\", userId).select().single();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Reset password\n   */ static async resetPassword(email) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Update password\n   */ static async updatePassword(newPassword) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.updateUser({\n            password: newPassword\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Listen to auth state changes with improved handling\n   */ static onAuthStateChange(callback) {\n        return _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            var _session_user;\n            console.log(\"Auth state change detected:\", event, session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.email);\n            // Add a small delay to ensure state consistency\n            setTimeout(()=>{\n                callback(event, session);\n            }, 100);\n        });\n    }\n    /**\n   * Check if current session is valid\n   */ static async isSessionValid() {\n        try {\n            const { data: { session }, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (error || !session) {\n                return false;\n            }\n            // Check if token is expired\n            const now = Math.floor(Date.now() / 1000);\n            if (session.expires_at && session.expires_at < now) {\n                console.log(\"Session token expired\");\n                return false;\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Error checking session validity:\", error);\n            return false;\n        }\n    }\n    /**\n   * Refresh session if needed\n   */ static async refreshSession() {\n        try {\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.refreshSession();\n            if (error) {\n                console.error(\"Error refreshing session:\", error);\n                return false;\n            }\n            return !!data.session;\n        } catch (error) {\n            console.error(\"Error in refreshSession:\", error);\n            return false;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/auth.ts\n"));

/***/ })

});