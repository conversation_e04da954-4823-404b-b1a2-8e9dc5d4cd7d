-- Fix Referral Placement Function Column Names
-- This migration fixes the inconsistency between find_next_placement_position return columns
-- and place_user_in_hierarchy function expectations

-- =====================================================
-- 1. FIX PLACE_USER_IN_HIERARCHY FUNCTION
-- =====================================================

-- Fix the function to use correct column names from find_next_placement_position
CREATE OR REPLACE FUNCTION public.place_user_in_hierarchy(
    new_user_id uuid, 
    referrer_id uuid
) 
RETURNS void 
LANGUAGE plpgsql 
AS $function$
DECLARE
    placement_info RECORD;
    referrer_level INTEGER;
    new_level INTEGER;
    ancestor_record RECORD;
    parent_path TEXT;
BEGIN
    -- Get referrer's level
    SELECT referral_level INTO referrer_level FROM users WHERE id = referrer_id;
    new_level := referrer_level + 1;
    
    -- Find placement position
    SELECT * INTO placement_info FROM find_next_placement_position(referrer_id);
    
    -- Use correct column names: parent_id and position (not placement_parent_id/placement_position)
    IF placement_info.parent_id IS NULL THEN
        RAISE EXCEPTION 'No placement position found for user %', new_user_id;
    END IF;
    
    -- Get parent's referral path (handle null values)
    SELECT COALESCE(referral_path, '') INTO parent_path FROM users WHERE id = placement_info.parent_id;
    
    -- Update user's referral information
    UPDATE users SET
        referred_by_id = placement_info.parent_id,
        referral_level = new_level,
        referral_path = parent_path || '/' || new_user_id::TEXT
    WHERE id = new_user_id;
    
    -- Create placement record
    INSERT INTO referral_placements (parent_id, child_id, position, placement_type)
    VALUES (
        placement_info.parent_id, 
        new_user_id, 
        placement_info.position,
        CASE WHEN placement_info.parent_id = referrer_id THEN 'direct' ELSE 'spillover' END
    );
    
    -- Create hierarchy records for all ancestors (only if parent path exists)
    IF parent_path IS NOT NULL AND parent_path != '' THEN
        FOR ancestor_record IN 
            SELECT id, referral_level FROM users 
            WHERE id = ANY(string_to_array(trim(both '/' from parent_path), '/')::UUID[])
        LOOP
            INSERT INTO referral_hierarchy (user_id, ancestor_id, level_difference)
            VALUES (new_user_id, ancestor_record.id, new_level - ancestor_record.referral_level)
            ON CONFLICT (user_id, ancestor_id) DO NOTHING;
        END LOOP;
    END IF;
    
    -- Always create hierarchy record for direct parent
    INSERT INTO referral_hierarchy (user_id, ancestor_id, level_difference)
    VALUES (new_user_id, placement_info.parent_id, 1)
    ON CONFLICT (user_id, ancestor_id) DO NOTHING;
    
    -- Update referral counts
    UPDATE users SET 
        direct_referrals_count = direct_referrals_count + 1
    WHERE id = referrer_id;
    
    -- Update total downline counts for all ancestors
    UPDATE users SET 
        total_downline_count = total_downline_count + 1
    WHERE id IN (
        SELECT ancestor_id FROM referral_hierarchy WHERE user_id = new_user_id
    );
END;
$function$;

-- =====================================================
-- 2. ADD COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON FUNCTION public.place_user_in_hierarchy(uuid, uuid) IS 
'Places a new user in the referral hierarchy under the specified referrer. Fixed to use correct column names from find_next_placement_position function (parent_id, position).';

-- =====================================================
-- 3. VERIFICATION QUERIES (FOR TESTING)
-- =====================================================

-- Test the function works correctly:
-- SELECT * FROM find_next_placement_position('some-uuid');
-- This should return columns: parent_id, position
